

class CountManager {
    static updateCount(rowData, rowElement) {
        const keywords = rowData.keywords || "";
        const description = rowData.description || "";
        const keywordCount = keywords ? keywords.split(',').filter(k => k.trim()).length : 0;
        const charCount = description.length;
        const wordCount = description.trim() ? description.trim().split(/\s+/).length : 0;
        const countCell = rowElement?.querySelector('.count-container');
        if (countCell) {
            countCell.innerHTML = `
                <span class="badge bg-primary" title="Description characters">${charCount} Char</span>
                <span class="badge bg-info" title="Description words">${wordCount} Words</span>
                <span class="badge bg-secondary" title="Keywords">${keywordCount} Keywords</span>
            `;
        }
        return { keywordCount, charCount, wordCount };
    }
}


class AppCore {
    constructor() {
        this.table = null;
        this.metadataResults = [];
    }
    async initialize() {
        this.setupDOMListeners();
        await this.initializeComponents();
    }
    setupDOMListeners() {
        document.addEventListener('DOMContentLoaded', async () => {
            await this.handleDOMReady();
        });
    }
    async handleDOMReady() {
        this.cleanupExistingModals();
        await this.initializeApplicationState();
    }
    cleanupExistingModals() {
        const existingModals = document.querySelectorAll('.modal');
        const preserveModalIds = ['newTableModal', 'moreTemplatesModal', 'deleteTemplateModal']; // List of modals to preserve

        existingModals.forEach(modal => {
            // Skip modals that should be preserved
            if (modal.id && preserveModalIds.includes(modal.id)) {
                console.log(`Preserving modal: ${modal.id}`);
                return;
            }

            // Dispose and remove other modals
            try {
                const instance = bootstrap.Modal.getInstance(modal);
                if (instance) instance.dispose();
                modal.remove();
            } catch (error) {
                console.warn(`Error cleaning up modal: ${error.message}`);
            }
        });
    }
    async initializeApplicationState() {
        this.initializeTable();
        this.setupEventListeners();
        this.updateUIState();
        this.updateExportButtonsState();
        setTimeout(() => this.initializeManagers(), 500);
    }
    initializeManagers() {
        if (this.table?.initialized) {
            this.table.redraw(true);
        }
        ['initPromptManager', 'initSettingsManager', 'initCsvManager'].forEach(manager => {
            if (typeof window[manager] === 'function') {
                window[manager]();
            }
        });
    }
}
class TableManager {
    constructor() {
        this.table = null;
    }
    initialize() {
        this.table = new Tabulator("#metadataTable", {
            data: metadataResults,
            index: "name",
            layout: "fitColumns",
            pagination: "local",
            paginationSize: 10,
            paginationSizeSelector: [5, 10, 20, 50, true],
            movableColumns: true,
            selectable: true,
            rowContextMenu: rowMenu,
            placeholder: "No Data Available - Please Select Images to Process",
            columns: this.getTableColumns()
        });
        this.setupTableEvents();
    }
    setupTableEvents() {
        this.table.on("cellEdited", (cell) => {
            const row = cell.getRow();
            const rowData = row.getData();
            const rowElement = row.getElement();
            if (rowElement) {
                 CountManager.updateCount(rowData, rowElement);
            }
        });
    }
    getTableColumns() {
        return [
            {
                formatter: "rownum",
                hozAlign: "center",
                headerSort: false,
                width: 40,
                title: "No.",
                download: false,
                print: false
            },
            {
                title: "Preview",
                field: "preview",
                formatter: "html",
                width: 120,
                headerSort: false,
                hozAlign: "center",
                tooltip: true,
                download: false,
                print: false
            }
        ];
    }
}
class ImageProcessor {
    constructor() {
        this.shouldStopProcessing = false;
    }
    async processImages(useParallel = false) {
        if (!useParallel) {
            showToast("Processing sequentially (Web Workers disabled or unavailable).", "info");
        }
        try {
            await this.executeProcessing();
            this.handleProcessingCompletion();
        } catch (error) {
            console.error("Error during sequential processing:", error);
            showToast("An error occurred during processing.", "error");
        } finally {
            this.finishProcessing();
        }
    }
    async processImage(image, index) {
        if (!this.validateImageIndex(index)) return;
        try {
            this.updateProcessingStatus(index);
            const metadata = await callGeminiAPI(image);
            this.updateMetadata(index, metadata);
        } catch (error) {
            this.handleProcessingError(error, image, index);
        }
    }
    validateImageIndex(index) {
        if (index < 0 || index >= metadataResults.length) {
            console.error("Invalid index in processImage:", index);
            return false;
        }
        return true;
    }
    updateProcessingStatus(index) {
        updateTableWithMetadata(index, null, "Processing...");
    }
    updateMetadata(index, metadata) {
        updateTableWithMetadata(index, metadata, "Completed");
    }
    handleProcessingError(error, image, index) {
        console.error(`Error processing image ${image.name} at index ${index}:`, error);
        updateTableWithMetadata(index, null, `Error: ${error.message || "Unknown error"}`);
    }
    async executeProcessing() { console.warn("ImageProcessor.executeProcessing not implemented"); }
    handleProcessingCompletion() { console.warn("ImageProcessor.handleProcessingCompletion not implemented"); }
    finishProcessing() { console.warn("ImageProcessor.finishProcessing not implemented"); }
}
class CSVExporter {
    constructor() {
        this.parser = Papa;
    }
    export(data) {
        if (!data || data.length === 0) {
            throw new Error("No data to export");
        }
        try {
            const csv = this.parser.unparse(data, {
                delimiter: ",",
                header: true,
                quotes: true,
                newline: "\r\n"
            });
            this.downloadCSV(csv);
        } catch (error) {
            throw new Error(`CSV export failed: ${error.message}`);
        }
    }
    downloadCSV(csv) {
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        link.setAttribute('download', `image_metadata_${timestamp}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}
class ExportManager {
    constructor() {
        this.csvExporter = new CSVExporter();
    }
    exportToCSV() {
        if (!this.validateExportData()) return;
        try {
            const csvData = this.prepareExportData();
            this.csvExporter.export(csvData);
            showToast("CSV exported successfully.", "success");
        } catch (error) {
            this.handleExportError(error);
        }
    }
    validateExportData() {
        if (metadataResults.length === 0) {
            showToast("No data to export.", "warning");
            return false;
        }
        return true;
    }
    prepareExportData() {
        return metadataResults.map(item => ({
            Filename: item.name,
            Title: item.title,
            Description: item.description,
            Keywords: item.keywords,
            Categories: item.shutterstockCategories,
            Category: item.adobestockCategory
        }));
    }
    handleExportError(error) {
        console.error("Error exporting CSV:", error);
        showToast("Error exporting CSV.", "error");
    }
}
class KeywordManager {
    addKeywordToPreview() {
        const container = document.getElementById('keywordsContainer');
        const newKeyword = this.promptForKeyword();
        if (this.isValidKeyword(newKeyword)) {
            this.addKeywordIfUnique(newKeyword, container);
        }
    }
    promptForKeyword() {
        return prompt("Enter new keyword:");
    }
    isValidKeyword(keyword) {
        return keyword && keyword.trim();
    }
    addKeywordIfUnique(keyword, container) {
        const cleanKeyword = keyword.trim().toLowerCase();
        const existingKeywords = this.getExistingKeywords(container);
        if (!existingKeywords.includes(cleanKeyword)) {
            this.createKeywordElement(cleanKeyword, container);
        } else {
            showToast(`Keyword "${cleanKeyword}" already exists.`, "warning");
        }
    }
    getExistingKeywords(container) {
        return Array.from(container.querySelectorAll('.keyword-tag'))
            .map(el => el.dataset.keyword.toLowerCase());
    }
    createKeywordElement(keyword, container) {
        const newIndex = container.querySelectorAll('.keyword-tag').length;
        addKeywordElement(keyword, newIndex, container);
        updateKeywordCount();
        setupDragAndDrop();
    }
}