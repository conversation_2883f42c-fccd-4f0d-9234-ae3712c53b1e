function resizeImage(file, maxDimension = 1000) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
            let width = img.width;
            let height = img.height;
            if (width > height && width > maxDimension) {
                height = Math.round(height * (maxDimension / width));
                width = maxDimension;
            } else if (height > maxDimension) {
                width = Math.round(width * (maxDimension / height));
                height = maxDimension;
            }
            if (img.width <= maxDimension && img.height <= maxDimension) {
                resolve(file);
                return;
            }
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0, width, height);
            canvas.toBlob((blob) => {
                blob.name = file.name;
                blob.lastModified = file.lastModified;
                resolve(blob);
            }, file.type, 0.9);
        };
        img.onerror = () => {
            reject(new Error('Failed to load image for resizing'));
        };
        const reader = new FileReader();
        reader.onload = (e) => img.src = e.target.result;
        reader.onerror = () => reject(new Error('Failed to read image file'));
        reader.readAsDataURL(file);
    });
}
function convertImageToBase64(file, resize = true, maxDimension = 1000) {
    return new Promise(async (resolve, reject) => {
        try {
            // Validasi tipe data
            if (!(file instanceof Blob)) {
                // Jika sudah string base64, kembalikan langsung
                if (typeof file === 'string' && file.startsWith('data:')) {
                    return resolve(file);
                }
                throw new Error('Invalid file type. Expected Blob or File object.');
            }

            // Special handling for images from imagen-subtab
            if (file.fromImagen) {
                console.log(`Converting imagen image to base64: ${file.name}`);

                // Always convert imagen images to PNG format to ensure compatibility
                try {
                    // Create an image element to load the image
                    const img = new Image();
                    const imgPromise = new Promise((resolve, reject) => {
                        img.onload = () => {
                            // Create a canvas to draw the image
                            const canvas = document.createElement('canvas');
                            canvas.width = img.width;
                            canvas.height = img.height;
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(img, 0, 0);

                            // Get the data URL directly from canvas
                            const dataUrl = canvas.toDataURL('image/png', 0.95);
                            resolve(dataUrl);
                        };
                        img.onerror = () => reject(new Error('Failed to load imagen image for conversion'));
                    });

                    // If we have originalSrc, use it directly
                    if (file.originalSrc) {
                        img.src = file.originalSrc;
                    } else {
                        // Otherwise read from the file
                        const reader = new FileReader();
                        reader.onload = (e) => img.src = e.target.result;
                        reader.onerror = () => reject(new Error('Failed to read imagen image file'));
                        reader.readAsDataURL(file);
                    }

                    // Wait for the conversion to complete
                    const dataUrl = await imgPromise;
                    console.log(`Successfully converted imagen image to base64 (${dataUrl.length} bytes)`);
                    return resolve(dataUrl);
                } catch (imagenError) {
                    console.error("Error converting imagen image:", imagenError);
                    // Fall back to standard processing if conversion fails
                }
            }

            // Check if file is a GIF
            if (file.type === 'image/gif') {
                // For GIF files, convert to PNG format to ensure compatibility with Gemini API
                try {
                    // Create an image element to load the GIF
                    const img = new Image();
                    img.onload = () => {
                        // Create a canvas to draw the first frame of the GIF
                        const canvas = document.createElement('canvas');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0);

                        // Convert to PNG format
                        canvas.toBlob((blob) => {
                            if (blob) {
                                // Now process the PNG blob
                                const pngReader = new FileReader();
                                pngReader.onload = () => resolve(pngReader.result);
                                pngReader.onerror = error => reject(error);
                                pngReader.readAsDataURL(blob);
                            } else {
                                reject(new Error('Failed to convert GIF to PNG format'));
                            }
                        }, 'image/png', 0.95);
                    };
                    img.onerror = () => {
                        reject(new Error('Failed to load GIF for conversion'));
                    };

                    // Read the GIF file
                    const gifReader = new FileReader();
                    gifReader.onload = (e) => img.src = e.target.result;
                    gifReader.onerror = () => reject(new Error('Failed to read GIF file'));
                    gifReader.readAsDataURL(file);
                    return;
                } catch (gifError) {
                    console.error("Error converting GIF to PNG:", gifError);
                    // Fall back to standard processing if conversion fails
                }
            }

            // Standard processing for non-GIF images
            const imageToConvert = resize ? await resizeImage(file, maxDimension) : file;
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
            reader.readAsDataURL(imageToConvert);
        } catch (error) {
            console.error("Error converting image:", error);
            // Validasi tipe data di blok catch juga
            if (file instanceof Blob) {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
                reader.readAsDataURL(file);
            } else if (typeof file === 'string' && file.startsWith('data:')) {
                resolve(file); // Jika sudah base64, kembalikan langsung
            } else {
                reject(new Error('Invalid file type. Expected Blob, File, or base64 string.'));
            }
        }
    });
}
async function callGeminiAPI(image, forceRegenerate = false, signal = null, type = 'all') { // Add type parameter
    // Check if API key rotation is enabled - define this early so it's available throughout the function
    const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

    // Log detailed information about the input file
    console.log(`callGeminiAPI called with: type=${type}, forceRegenerate=${forceRegenerate}`);
    console.log(`File details: name=${image.name}, type=${image.type}, size=${image.size}`);
    console.log(`File flags: isVideo=${image.isVideo || false}, fromVideo=${image.fromVideo || false}, fromImagen=${image.fromImagen || false}`);
    if (image.originalVideo) {
        console.log(`Has originalVideo reference: ${image.originalVideo.name}`);
    }
    if (image.thumbnailFile) {
        console.log(`Has thumbnailFile: ${image.thumbnailFile.name}`);
    }

    // Check if this image is from imagen-subtab (special handling)
    if (image && image.fromImagen) {
        console.log("Processing image from imagen-subtab:", image.name);

        // Force refresh the API key when processing images from imagen-subtab
        // This ensures we're using the most up-to-date API key
        if (enableApiKeyRotation && typeof window.getApiKey === 'function') {
            try {
                const freshApiKey = window.getApiKey();
                if (freshApiKey) {
                    // Update localStorage with the fresh key
                    localStorage.setItem('csvision_api_key', freshApiKey);
                    console.log(`Updated API key for imagen image: ••••${freshApiKey.slice(-4)}`);
                }
            } catch (error) {
                console.error("Error refreshing API key for imagen image:", error);
            }
        }
    }

    // Check if this is a video file - either by MIME type, isVideo flag, or fromVideo flag
    if (isVideoFile(image) || image.isVideo === true || image.fromVideo === true || (image.originalVideo && isVideoFile(image.originalVideo))) {
        // Get the selected model
        const selectedModel = localStorage.getItem('csvision_model') || 'gemini-2.0-flash';

        // Check if we should use direct video processing (Gemini 2.0 models support video)
        const useDirectVideoProcessing = selectedModel.startsWith('gemini-2.0') &&
                                        localStorage.getItem('csvision_use_direct_video') !== 'false';

        // If this is a thumbnail with fromVideo flag, we need to get the original video
        let videoFile = image;
        if (image.fromVideo && image.originalVideo) {
            console.log("Using original video from thumbnail with fromVideo flag");
            videoFile = image.originalVideo;
        }

        if (useDirectVideoProcessing) {
            try {
                console.log("Using direct video processing with Gemini API");
                // Use the new video API to process the video directly
                // Pass the type parameter to generateVideoMetadata
                try {
                    const metadata = await generateVideoMetadata(videoFile, signal, type);

                    // Ensure all required fields exist based on the requested type
                    if (type === 'all' || type === 'title') {
                        metadata.title = metadata.title || '';
                    }
                    if (type === 'all' || type === 'description') {
                        metadata.description = metadata.description || '';
                    }
                    if (type === 'all' || type === 'keywords') {
                        metadata.keywords = metadata.keywords || '';
                    }
                    if (type === 'all') {
                        metadata.shutterstockCategories = metadata.shutterstockCategories || '';
                        metadata.adobestockCategory = metadata.adobestockCategory || '';
                    }

                    return metadata;
                } catch (error) {
                    console.error("Error in generateVideoMetadata:", error);
                    // If the error is related to keywords, but we're not requesting keywords, we can ignore it
                    if (error.message && error.message.includes("keywords") && type !== 'all' && type !== 'keywords') {
                        console.log("Ignoring keywords error for non-keywords request");
                        // Return a partial metadata object with the requested fields
                        const partialMetadata = {};
                        if (type === 'title') partialMetadata.title = '';
                        if (type === 'description') partialMetadata.description = '';
                        return partialMetadata;
                    }
                    throw error;
                }
            } catch (videoError) {
                console.error("Error with direct video processing:", videoError);
                console.log("Falling back to thumbnail-based processing");
                // Fall back to thumbnail-based processing
                if (videoFile.thumbnailFile) {
                    image = videoFile.thumbnailFile;
                    // Mark the thumbnail as coming from a video
                    image.fromVideo = true;
                    image.originalVideo = videoFile;
                }
            }
        } else if (videoFile.thumbnailFile) {
            // Use the thumbnail for processing
            console.log("Using video thumbnail for API call");
            image = videoFile.thumbnailFile;
            // Mark the thumbnail as coming from a video
            image.fromVideo = true;
            image.originalVideo = videoFile;
        }
    }
    try {
        // Get API key - special handling for images from imagen-subtab
        let apiKey;
        if (image && image.fromImagen && enableApiKeyRotation) {
            // For imagen images, get the API key directly from localStorage to ensure consistency
            apiKey = localStorage.getItem('csvision_api_key') || '';
            console.log(`Using direct localStorage API key for imagen image: ••••${apiKey.slice(-4)}`);
        } else {
            // Normal flow for other images
            apiKey = getApiKey();
        }

        if (!apiKey) {
            const settingsTab = document.getElementById('settingsTab');
            if (settingsTab) {
                settingsTab.click();
            }
            throw new Error("API key is required. Please enter your Gemini API key in the Settings tab.");
        }

        // Get the selected prompt from the UI
        const promptList = document.getElementById('promptList');
        let selectedPrompt = null;

        // Access savedPrompts and defaultPrompts from window object
        if (promptList && promptList.selectedIndex !== -1 && window.savedPrompts && window.savedPrompts.length > 0) {
            selectedPrompt = window.savedPrompts[promptList.selectedIndex];
        } else if (window.savedPrompts && window.savedPrompts.length > 0) {
            // If no prompt is selected but we have saved prompts, use the first one
            selectedPrompt = window.savedPrompts[0];
        } else if (window.defaultPrompts && window.defaultPrompts.length > 0) {
            // If no saved prompts, use the first default prompt
            selectedPrompt = window.defaultPrompts[0];
        }

        // Get prompt values based on the type of metadata being generated
        let positivePrompt = "";
        let negativePrompt = "";
        let maxLength = 0;

        if (selectedPrompt) {
            // Use the detailed prompts based on the type of metadata being generated
            if (type === 'all' || type === 'title') {
                positivePrompt = selectedPrompt.positiveTitlePrompt || selectedPrompt.positive || "";
                negativePrompt = selectedPrompt.negativeTitlePrompt || selectedPrompt.negative || "";
                maxLength = selectedPrompt.titleLength || 10;
            } else if (type === 'description') {
                positivePrompt = selectedPrompt.positiveDescriptionPrompt || selectedPrompt.positive || "";
                negativePrompt = selectedPrompt.negativeDescriptionPrompt || selectedPrompt.negative || "";
                maxLength = selectedPrompt.descriptionLength || 250;
            } else if (type === 'keywords') {
                positivePrompt = selectedPrompt.positiveKeywordsPrompt || selectedPrompt.positive || "";
                negativePrompt = selectedPrompt.negativeKeywordsPrompt || selectedPrompt.negative || "";
                maxLength = selectedPrompt.keywordsCount || 20;
            } else {
                // If generating all metadata, use a combined prompt
                const positiveTitlePrompt = selectedPrompt.positiveTitlePrompt || "";
                const positiveDescriptionPrompt = selectedPrompt.positiveDescriptionPrompt || "";
                const positiveKeywordsPrompt = selectedPrompt.positiveKeywordsPrompt || "";

                const negativeTitlePrompt = selectedPrompt.negativeTitlePrompt || "";
                const negativeDescriptionPrompt = selectedPrompt.negativeDescriptionPrompt || "";
                const negativeKeywordsPrompt = selectedPrompt.negativeKeywordsPrompt || "";

                // Combine the prompts if they exist, otherwise use the legacy prompts
                if (positiveTitlePrompt || positiveDescriptionPrompt || positiveKeywordsPrompt) {
                    positivePrompt = `For Title: ${positiveTitlePrompt}\n\nFor Description: ${positiveDescriptionPrompt}\n\nFor Keywords: ${positiveKeywordsPrompt}`;
                } else {
                    positivePrompt = selectedPrompt.positive || "";
                }

                if (negativeTitlePrompt || negativeDescriptionPrompt || negativeKeywordsPrompt) {
                    negativePrompt = `For Title: ${negativeTitlePrompt}\n\nFor Description: ${negativeDescriptionPrompt}\n\nFor Keywords: ${negativeKeywordsPrompt}`;
                } else {
                    negativePrompt = selectedPrompt.negative || "";
                }
            }
        } else {
            // Fallback to localStorage values if no prompt is available
            positivePrompt = localStorage.getItem('csvision_positive_prompt') || "Describe this image in detail and provide relevant keywords";
            negativePrompt = localStorage.getItem('csvision_negative_prompt') || "";
        }

        // Get the selected model from localStorage or use a default
        let selectedModel = localStorage.getItem('csvision_model') || 'gemini-2.0-flash';

        // Check if the model is gemini-1.5-pro-vision and replace it with gemini-1.5-pro
        // This is because gemini-1.5-pro-vision has been deprecated
        if (selectedModel === 'gemini-1.5-pro-vision') {
            selectedModel = 'gemini-1.5-pro';
            // Update localStorage with the new model
            localStorage.setItem('csvision_model', selectedModel);
            console.log('Updated model from gemini-1.5-pro-vision to gemini-1.5-pro');
        }

        const maxDescLength = type === 'description' ? maxLength : (parseInt(localStorage.getItem('csvision_description_length')) || 250);
        const maxKeywords = type === 'keywords' ? maxLength : (parseInt(localStorage.getItem('csvision_keywords_count')) || 20);
        const maxTitleLength = type === 'title' ? maxLength : (selectedPrompt?.titleLength || 10);

        // Determine if this is a video or image file
        const isVideo = image.type && image.type.startsWith('video/');

        // Define category lists based on file type
        const videoCategoriesList = "Animals/Wildlife, Arts, Backgrounds/Textures, Buildings/Landmarks, Business/Finance, Education, Food and drink, Healthcare/Medical, Holidays, Industrial, Nature, Objects, People, Religion, Science, Signs/Symbols, Sports/Recreation, Technology, Transportation";

        const imageCategoriesList = "Abstract, Animals/Wildlife, Arts, Backgrounds/Textures, Beauty/Fashion, Buildings/Landmarks, Business/Finance, Celebrities, Education, Food and drink, Healthcare/Medical, Holidays, Industrial, Interiors, Miscellaneous, Nature, Objects, Parks/Outdoor, People, Religion, Science, Signs/Symbols, Sports/Recreation, Technology, Transportation, Vintage";

        const adobeStockCategoriesList = "1. Animals, 2. Buildings and Architecture, 3. Business, 4. Drinks, 5. The Environment, 6. States of Mind, 7. Food, 8. Graphic Resources, 9. Hobbies and Leisure, 10. Industry, 11. Landscapes, 12. Lifestyle, 13. People, 14. Plants and Flowers, 15. Culture and Religion, 16. Science, 17. Social Issues, 18. Sports, 19. Technology, 20. Transport, 21. Travel";

        // Build the enhanced prompt with specific instructions including categories
        let enhancedPrompt = "";

        // Add category selection instructions based on file type with focus on function/POV
        const categoryInstructions = isVideo
            ? `For Categories field: Analyze the PRIMARY FUNCTION and PURPOSE of this video content, then select 1-2 categories from this video categories list (${videoCategoriesList}). Focus on what the video is USED FOR or its INTENDED PURPOSE, not just what objects are visible. Examples: A video showing a laptop should be "Technology" or "Business/Finance" based on its context, not "Objects". A cooking video should be "Food and drink" regardless of kitchen tools shown. For Category field: Based on the video's main FUNCTION and CONTEXT, select only one category number from this Adobe Stock list (${adobeStockCategoriesList}).`
            : `For Categories field: Analyze the PRIMARY FUNCTION and PURPOSE of this image content, then select 1-2 categories from this image categories list (${imageCategoriesList}). Focus on what the image is USED FOR or its INTENDED PURPOSE, not just what objects are visible. Examples: A photo of a laptop should be "Technology" or "Business/Finance" based on its context, not "Objects". A food photo should be "Food and drink" regardless of plates/utensils shown. For Category field: Based on the image's main FUNCTION and CONTEXT, select only one category number from this Adobe Stock list (${adobeStockCategoriesList}).`;

        if (type === 'title') {
            enhancedPrompt = `${positivePrompt}. Create a concise title with maximum ${maxTitleLength} words. Format the response as: Title: [title]`;
        } else if (type === 'description') {
            enhancedPrompt = `${positivePrompt}. Write a detailed description with maximum ${maxDescLength} characters. Format the response as: Description: [description]`;
        } else if (type === 'keywords') {
            enhancedPrompt = `${positivePrompt}. Provide exactly ${maxKeywords} relevant keywords. Format the response as: Keywords: [keywords]`;
        } else {
            enhancedPrompt = `${positivePrompt}. Provide a title with maximum ${maxTitleLength} words, a description with maximum ${maxDescLength} characters, and exactly ${maxKeywords} keywords. ${categoryInstructions} Format the response as: Title: [title] Description: [description] Keywords: [keywords] Categories: [categories] Category: [category number]`;
        }

        // Add negative prompt if available
        if (negativePrompt) {
            enhancedPrompt += `\n\nDO NOT: ${negativePrompt}`;
        }

        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${selectedModel}:generateContent`;

        console.log(`Converting image to base64: ${image.name}, type: ${image.type}, fromImagen: ${image.fromImagen || false}`);
        const base64Image = await convertImageToBase64(image, true, 1000);
        if (base64Image.length > 10 * 1024 * 1024) {
            throw new Error("Image too large. Please use an image smaller than 10MB.");
        }

        console.log(`Base64 image size: ${base64Image.length} bytes, starts with: ${base64Image.substring(0, 50)}...`);

        // Pastikan mime_type ada
        const mimeType = image.type || 'image/jpeg';
        console.log(`Using MIME type: ${mimeType}`);

        const requestData = {
            contents: [{
                parts: [
                    {
                        text: enhancedPrompt
                    },
                    {
                        inline_data: {
                            mime_type: mimeType,
                            data: base64Image.split(',')[1]
                        }
                    }
                ]
            }],
            generation_config: {
                temperature: 0.4,
                max_output_tokens: 1024
            }
        };

        const response = await fetch(`${apiUrl}?key=${apiKey}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestData),
            signal: signal // Pass signal to fetch
        });

        if (!response.ok) {
            if (signal?.aborted) {
                throw new DOMException('Aborted', 'AbortError');
            }
            console.error("API Error:", response.status, response.statusText);
            const errorText = await response.text();
            console.error("Error details:", errorText);

            // Log additional debugging information
            console.log("API Request URL:", apiUrl);
            console.log("API Key used (last 4 chars):", apiKey ? `••••${apiKey.slice(-4)}` : 'none');
            console.log("API Key rotation enabled:", enableApiKeyRotation);
            console.log("Model used:", localStorage.getItem('csvision_model') || 'gemini-2.0-flash');

            // Check if this is a GIF file that's causing problems
            if (image.type === 'image/gif' && (response.status === 400 || response.status === 404)) {
                console.log("GIF format detected with error. Converting to PNG and retrying...");

                try {
                    // Create a new image element to load the GIF
                    const img = new Image();
                    const imgPromise = new Promise((resolve, reject) => {
                        img.onload = () => {
                            // Create a canvas to draw the first frame of the GIF
                            const canvas = document.createElement('canvas');
                            canvas.width = img.width;
                            canvas.height = img.height;
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(img, 0, 0);

                            // Convert to PNG format
                            canvas.toBlob((blob) => {
                                if (blob) {
                                    // Create a new File object with PNG type
                                    const pngFile = new File([blob], image.name.replace('.gif', '.png'), {
                                        type: 'image/png',
                                        lastModified: new Date().getTime()
                                    });
                                    resolve(pngFile);
                                } else {
                                    reject(new Error('Failed to convert GIF to PNG format'));
                                }
                            }, 'image/png', 0.9);
                        };
                        img.onerror = () => reject(new Error('Failed to load GIF for conversion'));
                    });

                    // Load the GIF image
                    const reader = new FileReader();
                    reader.onload = (e) => img.src = e.target.result;
                    reader.readAsDataURL(image);

                    // Wait for the conversion to complete
                    const pngFile = await imgPromise;
                    console.log("GIF successfully converted to PNG. Retrying API call...");

                    // Retry with the PNG file
                    return await callGeminiAPI(pngFile, forceRegenerate, signal, type);
                } catch (conversionError) {
                    console.error("Error converting GIF to PNG:", conversionError);
                    // Continue with normal error handling if conversion fails
                }
            }

            // Mark the current API key as failed
            if (typeof window.markApiKeyAsFailed === 'function') {
                window.markApiKeyAsFailed(apiKey);
            }

            // Try with the next API key if rotation is enabled
            if (enableApiKeyRotation && typeof window.getNextApiKey === 'function') {
                const nextKey = window.getNextApiKey();
                if (nextKey) {
                    // Find the API key name
                    let nextKeyName = "Unknown";
                    if (window.apiKeys && window.apiKeys.length > 0) {
                        const activeKey = window.apiKeys.find(key => key.active);
                        if (activeKey) {
                            nextKeyName = activeKey.name;
                        }
                    }
                    console.log(`Trying next API key: ${nextKeyName} (••••${nextKey.slice(-4)})`);

                    // Update localStorage with the new active key to ensure it's used in the next API call
                    localStorage.setItem('csvision_api_key', nextKey);

                    // Retry the API call with the same parameters
                    return await callGeminiAPI(image, forceRegenerate, signal, type);
                }
            }

            // If rotation is not enabled or no more keys available, try fallback model
            if (response.status === 400 && !forceRegenerate) {
                const fallbackModel = 'gemini-1.5-pro';
                console.log(`Trying fallback model: ${fallbackModel}`);
                localStorage.setItem('csvision_model', fallbackModel);
                return await callGeminiAPI(image, true, signal, type);
            }

            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const responseData = await response.json();
        if (!responseData.candidates || !responseData.candidates[0] || !responseData.candidates[0].content) {
            throw new Error("Invalid API response format");
        }

        const text = responseData.candidates[0].content.parts[0].text;
        const result = parseGeminiResponse(text);

        // Terapkan batasan setelah parsing
        if (result.description) {
            result.description = result.description.slice(0, maxDescLength);
        }

        if (result.keywords) {
            const keywords = result.keywords.split(',').map(k => k.trim());
            result.keywords = keywords.slice(0, maxKeywords).join(', ');
        }

        if (result.title && maxTitleLength > 0) {
            // Limit title to maxTitleLength words
            const words = result.title.split(' ');
            if (words.length > maxTitleLength) {
                result.title = words.slice(0, maxTitleLength).join(' ');
            }
        }

        return result;
    } catch (error) {
        console.error("Error in callGeminiAPI:", error);
        throw error;
    }
}

async function callGeminiAlternativeAPI(image, prompt, base64Image, signal = null) { // Add signal parameter
    // Check if API key rotation is enabled - define this early so it's available throughout the function
    const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

    // Get API key - special handling for images from imagen-subtab
    let apiKey;
    if (image && image.fromImagen && enableApiKeyRotation) {
        // For imagen images, get the API key directly from localStorage to ensure consistency
        apiKey = localStorage.getItem('csvision_api_key') || '';
        console.log(`Using direct localStorage API key for imagen image in alternative API: ••••${apiKey.slice(-4)}`);
    } else {
        // Normal flow for other images
        apiKey = getApiKey();
    }

    if (!apiKey) {
        throw new Error("API key is required. Please enter a valid Gemini API key.");
    }
    const altApiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent";
    console.log(`Alternative API - Image type: ${image.type}, fromImagen: ${image.fromImagen || false}`);
    console.log(`Alternative API - Base64 image size: ${base64Image.length} bytes`);

    const requestData = {
        contents: [{
            parts: [
                { text: prompt },
                {
                    inline_data: {
                        mime_type: image.type || 'image/jpeg',
                        data: base64Image.split(',')[1]
                    }
                }
            ]
        }],
        generation_config: {
            temperature: 0.4,
            max_output_tokens: 1024
        }
    };
    const response = await fetch(`${altApiUrl}?key=${apiKey}`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify(requestData),
        signal: signal // Pass signal to fetch
    });
    if (!response.ok) {
        if (signal?.aborted) {
            throw new DOMException('Aborted', 'AbortError');
        }
        console.error("Alternative API Error:", response.status, response.statusText);
        const errorText = await response.text();
        console.error("Error details:", errorText);

        // Log additional debugging information
        console.log("Alternative API Request URL:", altApiUrl);
        console.log("API Key used (last 4 chars):", apiKey ? `••••${apiKey.slice(-4)}` : 'none');
        console.log("API Key rotation enabled:", localStorage.getItem('csvision_enable_api_key_rotation') === 'true');
        console.log("Model used: gemini-1.5-pro (alternative API)");

        // Check if this is a GIF file that's causing problems
        if (image.type === 'image/gif' && (response.status === 400 || response.status === 404)) {
            console.log("GIF format detected with error in alternative API. Converting to PNG and retrying...");

            try {
                // Create a new image element to load the GIF
                const img = new Image();
                const imgPromise = new Promise((resolve, reject) => {
                    img.onload = () => {
                        // Create a canvas to draw the first frame of the GIF
                        const canvas = document.createElement('canvas');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0);

                        // Convert to PNG format
                        canvas.toBlob((blob) => {
                            if (blob) {
                                // Create a new File object with PNG type
                                const pngFile = new File([blob], image.name.replace('.gif', '.png'), {
                                    type: 'image/png',
                                    lastModified: new Date().getTime()
                                });

                                // Convert the PNG to base64
                                const pngReader = new FileReader();
                                pngReader.onload = (e) => {
                                    const pngBase64 = e.target.result;
                                    resolve({file: pngFile, base64: pngBase64});
                                };
                                pngReader.onerror = (e) => reject(new Error('Failed to convert PNG to base64'));
                                pngReader.readAsDataURL(blob);
                            } else {
                                reject(new Error('Failed to convert GIF to PNG format'));
                            }
                        }, 'image/png', 0.9);
                    };
                    img.onerror = () => reject(new Error('Failed to load GIF for conversion'));
                });

                // Load the GIF image
                const reader = new FileReader();
                reader.onload = (e) => img.src = e.target.result;
                reader.readAsDataURL(image);

                // Wait for the conversion to complete
                const pngData = await imgPromise;
                console.log("GIF successfully converted to PNG. Retrying alternative API call...");

                // Retry with the PNG file
                return await callGeminiAlternativeAPI(pngData.file, prompt, pngData.base64, signal);
            } catch (conversionError) {
                console.error("Error converting GIF to PNG in alternative API:", conversionError);
                // Continue with normal error handling if conversion fails
            }
        }

        // Mark the current API key as failed
        if (typeof window.markApiKeyAsFailed === 'function') {
            window.markApiKeyAsFailed(apiKey);
        }

        // Try with the next API key if rotation is enabled
        if (enableApiKeyRotation && typeof window.getNextApiKey === 'function') {
            const nextKey = window.getNextApiKey();
            if (nextKey) {
                // Find the API key name
                let nextKeyName = "Unknown";
                if (window.apiKeys && window.apiKeys.length > 0) {
                    const activeKey = window.apiKeys.find(key => key.active);
                    if (activeKey) {
                        nextKeyName = activeKey.name;
                    }
                }
                console.log(`Trying next API key: ${nextKeyName} (••••${nextKey.slice(-4)})`);

                // Update localStorage with the new active key to ensure it's used in the next API call
                localStorage.setItem('csvision_api_key', nextKey);

                // Retry the API call with the same parameters
                return await callGeminiAlternativeAPI(image, prompt, base64Image, signal);
            }
        }

        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    const responseData = await response.json();
    if (!responseData.candidates || !responseData.candidates[0] || !responseData.candidates[0].content) {
        throw new Error("Invalid API response format");
    }
    const text = responseData.candidates[0].content.parts[0].text;
    return parseGeminiResponse(text);
}
function parseGeminiResponse(text) {
    const result = {
        title: '',
        description: '',
        keywords: '',
        shutterstockCategories: '',
        adobestockCategory: ''
    };

    try {
        text = text.replace(/^(here is|here are|and keywords for the image|following all the rules|following your instructions|as requested).*?:/i, "");
        text = text.replace(/^.*?(title:|description:|keywords:)/i, "$1");
        text = text.replace(/\*\*/g, "");

        let titleMatch = text.match(/title:?\s*([\s\S]*?)(?=description:|keywords:|$)/i);
        if (titleMatch && titleMatch[1]) {
            result.title = titleMatch[1].trim();
        }

        let descriptionMatch = text.match(/description:?\s*([\s\S]*?)(?=keywords:|kata kunci:|tags:|$)/i);
        if (descriptionMatch && descriptionMatch[1]) {
            result.description = descriptionMatch[1].trim();
        } else {
            const paragraphs = text.split('\n\n');
            if (paragraphs.length > 0) {
                result.description = paragraphs[0].trim();
            }
        }
        let keywordsMatch = text.match(/(?:keywords:|kata kunci:|tags:)\s*([\s\S]*?)(?=categories:|category:|$)/i);
        if (keywordsMatch && keywordsMatch[1]) {
            let keywords = keywordsMatch[1].trim();
            keywords = keywords.replace(/description:[\s\S]*?(?=keywords:|$)/i, "");
            keywords = keywords.replace(/^[-*•]|\d+\.\s+/gm, '').trim();
            keywords = keywords.replace(/\n+/g, ', ');
            keywords = keywords.replace(/,\s*,/g, ',');
            keywords = keywords.replace(/^for the image:?,?\s*/i, "");
            keywords = keywords.split(',').map(k => k.trim().replace(/\.$/, '')).join(', ');
            result.keywords = keywords;
        }

        // Parse Categories (Shutterstock categories)
        let categoriesMatch = text.match(/(?:categories:)\s*([\s\S]*?)(?=category:|$)/i);
        if (categoriesMatch && categoriesMatch[1]) {
            let categories = categoriesMatch[1].trim();
            categories = categories.replace(/^[-*•]|\d+\.\s+/gm, '').trim();
            categories = categories.replace(/\n+/g, ', ');
            categories = categories.replace(/,\s*,/g, ',');
            result.shutterstockCategories = categories;
        }

        // Parse Category (Adobe Stock category number)
        let categoryMatch = text.match(/(?:category:)\s*([\s\S]*?)(?=$)/i);
        if (categoryMatch && categoryMatch[1]) {
            let category = categoryMatch[1].trim();
            category = category.replace(/^[-*•]|\d+\.\s+/gm, '').trim();
            // Extract just the number if it's in format "1. Animals" or just "1"
            const numberMatch = category.match(/^(\d+)/);
            if (numberMatch) {
                result.adobestockCategory = numberMatch[1];
            } else {
                result.adobestockCategory = category;
            }
        }
        if (!result.keywords) {
            const listMatch = text.match(/(?:^|\n)(?:[-*•]|\d+\.\s+)([^\n]+)(?:\n|$)/gm);
            if (listMatch) {
                const keywords = listMatch.map(item =>
                    item.replace(/^[-*•]|\d+\.\s+/gm, '').trim()
                ).join(', ');
                result.keywords = keywords;
            }
        }
        result.description = result.description.replace(/^description:?\s*/i, "").trim();
        result.keywords = result.keywords.replace(/^keywords:?\s*/i, "").trim();
        result.keywords = ensureLowercaseKeywords(result.keywords);
        if (result.keywords && result.keywords.toLowerCase().includes("description:")) {
            const cleanKeywords = result.keywords.match(/keywords:?\s*([\s\S]*?)$/i);
            if (cleanKeywords && cleanKeywords[1]) {
                result.keywords = cleanKeywords[1].trim();
                result.keywords = ensureLowercaseKeywords(result.keywords);
            }
        }
        result.description = result.description.replace(/^(here is|here are|and keywords for the image|following all the rules|following your instructions|as requested).*?:/i, "").trim();
        result.description = result.description.replace(/^description:?\s*/i, "").trim();
        if (result.description.match(/^.*?description:/i)) {
            result.description = result.description.replace(/^.*?description:/i, "").trim();
        }

        // Terapkan batasan setelah parsing
        const maxDescLength = parseInt(localStorage.getItem('csvision_description_length')) || 250;
        const maxKeywords = parseInt(localStorage.getItem('csvision_keywords_count')) || 20;

        if (result.description) {
            result.description = result.description.slice(0, maxDescLength);
        }

        if (result.keywords) {
            const keywords = result.keywords.split(',').map(k => k.trim());
            result.keywords = keywords.slice(0, maxKeywords).join(', ');
        }

        return result;
    } catch (error) {
        console.error("Error parsing Gemini response:", error);
        return result;
    }
}
function ensureLowercaseKeywords(keywords) {
    return keywords.split(',').map(keyword => keyword.trim().toLowerCase()).join(', ');
}

function getApiKey() {
    // Check if we should use the advanced API key management
    const enableApiKeyRotation = localStorage.getItem('csvision_enable_api_key_rotation') === 'true';

    // Get the current active API key from localStorage first
    const currentApiKey = localStorage.getItem('csvision_api_key') || '';

    if (enableApiKeyRotation && typeof window.getApiKey === 'function') {
        try {
            // Use the advanced API key management from settings-manager.js
            const rotatedKey = window.getApiKey();

            // Log the key being used (last 4 chars only for security)
            if (rotatedKey) {
                console.log(`Using API key from rotation: ••••${rotatedKey.slice(-4)}`);

                // Make sure the key is also set in localStorage for other components
                if (rotatedKey !== currentApiKey) {
                    localStorage.setItem('csvision_api_key', rotatedKey);
                }

                return rotatedKey;
            } else {
                console.warn("API key rotation enabled but no key returned from window.getApiKey()");
            }
        } catch (error) {
            console.error("Error getting API key from rotation:", error);
        }
    }

    // Fallback to simple localStorage method
    console.log(`Using API key from localStorage: ••••${currentApiKey.slice(-4)}`);
    return currentApiKey;
}
