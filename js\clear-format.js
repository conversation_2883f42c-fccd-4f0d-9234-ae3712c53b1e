function cleanKeywordsFormat(keywords, maxKeywords = 0) {
    if (!keywords) return "";
    let cleaned = keywords.replace(/[^a-zA-Z0-9\s,]/g, ''); 
    cleaned = cleaned
        .replace(/[;|•\t\n\r]/g, ',')
        .replace(/\s+[-–—]\s+/g, ', ')
        .replace(/\s*\/\s*/g, ', ')
        .replace(/\.\s+/g, ', ');
    cleaned = cleaned
        .replace(/\s+and\s+/gi, ', ')
        .replace(/\s+&\s+/g, ', ');
    cleaned = cleaned
        .replace(/,+/g, ',') 
        .replace(/\s*,\s*/g, ', '); 
    cleaned = cleaned.replace(/\s+/g, ' '); 
    cleaned = ensureLowercaseKeywords(cleaned);
    cleaned = removeDuplicateKeywords(cleaned);
    cleaned = cleaned.replace(/^,+|,+$/g, '').trim(); 
    if (maxKeywords > 0) {
        const keywordArray = cleaned.split(',').map(k => k.trim()).filter(k => k);
        if (keywordArray.length > maxKeywords) {
            cleaned = keywordArray.slice(0, maxKeywords).join(', ');
        }
    }
    return cleaned;
}
function removeDuplicateKeywords(keywords) {
    if (!keywords) return "";
    const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k);
    const uniqueKeywords = [...new Set(keywordArray)];
    return uniqueKeywords.join(', ');
}
function cleanDescriptionText(description) {
    if (!description) return "";
    const original = description;
    let cleanedText = description;
    const patternsToRemove = [
        /^(here is|here's|sure, here is|okay, here is|certainly, here is)\s*(the\s*)?(description|title|keywords).*?:?\s*/i,
        /^(description:|image description:|title:|keywords:|tags:)\s*/i,
        /\s*(keywords:|tags:).*$/gim, 
        /^(?:based on your request|as requested|following your instructions|according to the rules).*?:?\s*/i,
        /^(?:I have|I've) generated the (description|title|keywords).*?:?\s*/i,
        /^(?:the (description|title|keywords) for the image is|description of the image:)\s*/i,
        /\s*let me know if you need anything else\!?$/i,
        /\s*I hope this helps\!?$/i,
        /^(?:tailored to your specifications|created as requested|as per your request|according to your requirements).*?:/i,
        /^and\s+keywords(?:\s*,)?\s*(?:following\s+(?:all\s+)?your\s+(?:rules|guidelines|instructions|requirements))?:?/i,
        /^and\s+(?:set\s+of\s+)?keywords\s+(?:for\s+the\s+image)?(?:\s*,)?\s*(?:following\s+your\s+(?:rules|guidelines|instructions))?:?/i,
        /^(?:here is|here are|following your rules|following all the rules|as requested).*?:/i,
        /^(?:I've|I have)\s+created\s+(?:a|an|the)?\s+(?:description|image description|title|keywords).*?:/i,
        /^(?:As requested|As per your request),?\s+(?:here is|here are|I've provided|I have provided).*?:/i,
        /^\s*-\s*/, 
        /\n\s*-\s*/g, 
    ];
    patternsToRemove.forEach(pattern => {
        cleanedText = cleanedText.replace(pattern, "");
    });
    const colonPrefixMatch = cleanedText.match(/^([^.!?\n]{5,50}?):\s+/);
    if (colonPrefixMatch) {
        const prefix = colonPrefixMatch[1].toLowerCase();
        const metaWords = ['description', 'keyword', 'following', 'requested', 'instruction', 'guideline', 'rule', 'specification', 'detailed', 'image', 'title', 'tag'];
        const containsMetaWord = metaWords.some(word => prefix.includes(word));
        if (containsMetaWord || prefix.length < 10) { 
            cleanedText = cleanedText.replace(/^[^.!?\n]{5,50}?:\s+/, '');
        }
    }
    cleanedText = cleanedText.replace(/\n{3,}/g, "\n\n"); 
    cleanedText = cleanedText.trim();
    if (cleanedText === "" && original.trim() !== "") {
        cleanedText = original.replace(/^(description:|keywords:|title:)\s*/i, "").trim();
        if (cleanedText === "") {
            const firstSentence = original.match(/^.*?[.!?]/);
            return firstSentence ? firstSentence[0].trim() : original.trim();
        }
    }
    return cleanedText;
}
function getMaxKeywordsFromSettings() {
    const defaultValue = 20;
    try {
        const keywordsCountSlider = document.getElementById('keywordsCountSlider');
        if (keywordsCountSlider && keywordsCountSlider.value) {
            const sliderValue = parseInt(keywordsCountSlider.value, 10);
            if (!isNaN(sliderValue) && sliderValue > 0) {
                return sliderValue;
            }
        }
        const storedValue = localStorage.getItem('csvision_keywords_count');
        if (storedValue) {
            const localValue = parseInt(storedValue, 10);
            if (!isNaN(localValue) && localValue > 0) {
                return localValue;
            }
        }
    } catch (error) {
        console.warn("Error reading max keywords setting:", error);
    }
    return defaultValue;
}
function updateKeywordCount() {
    const container = document.getElementById('keywordsContainer');
    if (!container) return;
    const keywordElements = container.querySelectorAll('.keyword-tag');
    const keywordCount = keywordElements.length;
    const keywordCountBadge = document.getElementById('keywordCountBadge');
    if (keywordCountBadge) {
        keywordCountBadge.textContent = `${keywordCount} keyword${keywordCount !== 1 ? 's' : ''}`;
        keywordCountBadge.className = `badge bg-secondary ms-1`;
        const maxKeywords = getMaxKeywordsFromSettings();
        if (maxKeywords > 0 && keywordCount > maxKeywords) { 
            keywordCountBadge.className = `badge bg-danger ms-1`;
        }
    }
}


function exportToCSV() {
    if (metadataResults.length === 0) {
        showToast("No data to export.", "warning");
        return;
    }
    const csvData = metadataResults.map(item => ({
        Filename: item.name,
        Title: item.title,
        Description: item.description,
        Keywords: item.keywords,
        Categories: item.shutterstockCategories,
        Category: item.adobestockCategory
    }));
    try {
        const csv = Papa.unparse(csvData, {
            delimiter: ",",
            header: true,
            quotes: true,
            newline: "\r\n"
        });
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        link.setAttribute('download', `image_metadata_${timestamp}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        showToast("CSV exported successfully.", "success");
    } catch (error) {
        console.error("Error exporting CSV:", error);
        showToast("Error exporting CSV.", "error");
    }
}